import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { SiteEngineer } from '../types/auth';

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

class SiteEngineerApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.workforce.com',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.clearToken();
          // Redirect to login or emit event
        }
        return Promise.reject(error);
      }
    );
  }

  // Set authentication token
  setToken(token: string): void {
    this.token = token;
  }

  // Clear authentication token
  clearToken(): void {
    this.token = null;
  }

  // Authentication endpoints
  async login(email: string, password: string): Promise<ApiResponse<{ user: SiteEngineer; token: string }>> {
    try {
      const response = await this.api.post('/auth/login', { email, password });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async logout(): Promise<ApiResponse<void>> {
    try {
      const response = await this.api.post('/auth/logout');
      this.clearToken();
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Workers endpoints
  async getMyWorkers(engineerId: string): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.api.get(`/engineers/${engineerId}/workers`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async submitOvertime(workerId: string, overtimeData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post(`/workers/${workerId}/overtime`, overtimeData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Tasks endpoints
  async getMyTasks(engineerId: string): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.api.get(`/engineers/${engineerId}/tasks`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async createTask(taskData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post('/tasks', taskData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateTask(taskId: string, taskData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.put(`/tasks/${taskId}`, taskData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateTaskProgress(taskId: string, progress: number): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.put(`/tasks/${taskId}/progress`, { progress });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Permits endpoints
  async getMyPermits(engineerId: string): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.api.get(`/engineers/${engineerId}/permits`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async requestPermit(permitData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post('/permits/request', permitData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getPermitStatus(permitId: string): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.get(`/permits/${permitId}/status`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Reports endpoints
  async submitDailyReport(reportData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post('/reports/daily', reportData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async submitIssueReport(reportData: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post('/reports/issues', reportData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getMyReports(): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.api.get('/reports/my-reports');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handling
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.data?.error || 'An error occurred';
      return new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error. Please check your connection.');
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred');
    }
  }
}

export const siteEngineerApiService = new SiteEngineerApiService();

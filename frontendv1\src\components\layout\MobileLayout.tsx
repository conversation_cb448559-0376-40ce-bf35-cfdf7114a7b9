import React from 'react';
import MobileHeader from './MobileHeader';
import BottomNavigation from './BottomNavigation';

interface MobileLayoutProps {
  children: React.ReactNode;
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  showMenu?: boolean;
  onMenuClick?: () => void;
  headerActions?: React.ReactNode;
  notificationCount?: number;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  hideBottomNav?: boolean;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  title,
  showBack = false,
  onBack,
  showMenu = false,
  onMenuClick,
  headerActions,
  notificationCount = 0,
  activeTab,
  onTabChange,
  hideBottomNav = false
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col md:hidden">
      {/* Header */}
      <MobileHeader
        title={title}
        showBack={showBack}
        onBack={onBack}
        showMenu={showMenu}
        onMenuClick={onMenuClick}
        actions={headerActions}
        notificationCount={notificationCount}
      />

      {/* Main Content */}
      <main className={`flex-1 overflow-auto ${hideBottomNav ? 'pb-4' : 'pb-20'}`}>
        {children}
      </main>

      {/* Bottom Navigation */}
      {!hideBottomNav && (
        <BottomNavigation
          activeTab={activeTab}
          onTabChange={onTabChange}
        />
      )}
    </div>
  );
};

export default MobileLayout;

// Site Engineer specific types

export interface Certification {
  id: string;
  name: string;
  issuedBy: string;
  issuedDate: Date;
  expiryDate: Date;
  certificateNumber: string;
  isValid: boolean;
}

export interface Training {
  id: string;
  name: string;
  completedDate: Date;
  expiryDate?: Date;
  provider: string;
  isValid: boolean;
}

export interface SiteWorker {
  id: string;
  name: string;
  employeeId: string;
  trade: string;
  photo?: string;
  phone: string;
  email?: string;
  siteId: string;
  supervisorId: string; // Site engineer ID
  isActive: boolean;
  isOnSite: boolean;
  lastCheckIn?: Date;
  certifications: Certification[];
  trainings: Training[];
  hoursWorked: number;
  overtimeHours: number;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface WorkerSummary {
  id: string;
  name: string;
  trade: string;
  photo?: string;
  isOnSite: boolean;
  hoursWorked: number;
  overtimeHours: number;
  lastSeen: Date;
  status: 'on-site' | 'off-site' | 'on-break' | 'unavailable';
}

export interface OvertimeRequest {
  id: string;
  workerId: string;
  workerName: string;
  date: Date;
  hours: number;
  reason: string;
  description?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  submittedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
}

export interface WorkerAttendance {
  workerId: string;
  date: Date;
  checkInTime?: Date;
  checkOutTime?: Date;
  hoursWorked: number;
  overtimeHours: number;
  status: 'present' | 'absent' | 'late' | 'early-departure';
  notes?: string;
}

// Site Engineer Task Types
export interface SiteTask {
  id: string;
  title: string;
  description?: string;
  location: string;
  plannedDate: Date;
  estimatedHours: number;
  assignedWorkers: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  notes?: string;
  attachments?: string[];
}

export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  estimatedHours: number;
  requiredTrade?: string;
  requiredCertifications?: string[];
  safetyRequirements?: string[];
}

// Site Engineer Permit Types
export interface SitePermitRequest {
  id: string;
  workActivity: string;
  description: string;
  location: string;
  plannedStartDate: Date;
  plannedEndDate: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
  permitType: 'hot-work' | 'confined-space' | 'working-at-height' | 'excavation' | 'electrical' | 'general';
  requestedBy: string;
  requestedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  approvedBy?: string;
  approvedAt?: Date;
  rejectionReason?: string;
  conditions?: string[];
  attachments?: string[];
  relatedTasks?: string[];
}

// Site Engineer Report Types
export interface DailyReport {
  id: string;
  date: Date;
  siteId: string;
  engineerId: string;
  weather: {
    condition: string;
    temperature: number;
    description?: string;
  };
  workersPresent: number;
  workersAbsent: number;
  tasksCompleted: string[];
  tasksInProgress: string[];
  issues: {
    id: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    status: 'open' | 'resolved';
  }[];
  safetyIncidents: {
    id: string;
    description: string;
    severity: 'minor' | 'major' | 'critical';
    actionTaken: string;
  }[];
  materialDeliveries: {
    supplier: string;
    materials: string[];
    quantity: string;
    time: Date;
  }[];
  notes?: string;
  photos?: string[];
  submittedAt: Date;
}

export interface IssueReport {
  id: string;
  title: string;
  description: string;
  category: 'safety' | 'quality' | 'schedule' | 'material' | 'equipment' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: string;
  reportedBy: string;
  reportedAt: Date;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  assignedTo?: string;
  dueDate?: Date;
  resolution?: string;
  resolvedAt?: Date;
  photos?: string[];
  attachments?: string[];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

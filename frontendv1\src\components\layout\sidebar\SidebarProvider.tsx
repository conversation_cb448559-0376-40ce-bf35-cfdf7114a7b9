/**
 * Sidebar Context Provider
 * Centralized state management for all sidebar interactions and behavior
 */

import React, {
	createContext,
	useContext,
	useReducer,
	useMemo,
	useCallback,
	useRef,
	useEffect,
} from "react";
import { useLocation } from "react-router-dom";
import {
	BarChart3,
	Database,
	LayoutDashboard,
	Settings,
	Users,
	Clock,
	GraduationCap,
	ShieldCheck,
	ClipboardList,
	ClipboardCheck,
	HardHat,
	FileText,
} from "lucide-react";
import {
	SidebarState,
	SidebarAction,
	SidebarContextValue,
	SidebarProviderProps,
	MenuItem,
	SiteMenuItem,
	HoverConfig,
} from "../../../types/sidebar";
import { useSiteContext } from "../../../hooks/useSiteContext";
import {
	isPathActive,
	isSubmenuActive,
	getSitePath,
} from "../../../utils/routeUtils";
import {
	sidebarTokens,
	defaultHoverConfig,
} from "../../../styles/sidebar-tokens";

// ============================================================================
// MENU CONFIGURATION
// ============================================================================

/**
 * Mock sites data - in a real app, this would come from an API
 */
const getAllSites = () => [
	{
		id: "site1",
		name: "Westlands Construction Site",
		healthStatus: "green" as const,
		workersOnSite: 42,
		activePermits: 8,
		openIncidents: 0,
		projectManager: "John Mwangi",
		location: "Waiyaki Way, Westlands, Nairobi",
		timeline: "Jan 2025 - Dec 2026",
		currentPhase: "Foundation",
		progressPercentage: 25,
	},
	{
		id: "site2",
		name: "Mombasa Road Project",
		healthStatus: "amber" as const,
		workersOnSite: 36,
		activePermits: 5,
		openIncidents: 2,
		projectManager: "Sarah Ochieng",
		location: "Enterprise Road, Industrial Area",
		timeline: "Mar 2025 - Aug 2026",
		currentPhase: "Structural",
		progressPercentage: 40,
	},
	{
		id: "site3",
		name: "Thika Highway Expansion",
		healthStatus: "red" as const,
		workersOnSite: 28,
		activePermits: 3,
		openIncidents: 4,
		projectManager: "David Kimani",
		location: "Thika Road, Kasarani Junction",
		timeline: "Nov 2024 - Jul 2026",
		currentPhase: "Excavation",
		progressPercentage: 15,
	},
	{
		id: "site4",
		name: "Nakuru Commercial Plaza",
		healthStatus: "green" as const,
		workersOnSite: 15,
		activePermits: 2,
		openIncidents: 0,
		projectManager: "Grace Wanjiku",
		location: "Kenyatta Avenue, Nakuru CBD",
		timeline: "Apr 2025 - Mar 2026",
		currentPhase: "Planning",
		progressPercentage: 5,
	},
];

/**
 * Returns the main menu items for company-level navigation
 */
const getMainMenuItems = (): MenuItem[] => {
	const sites = getAllSites();

  return [
    {
      name: 'Dashboard',
      icon: <LayoutDashboard className="h-6 w-6" />,
      path: '/',
      submenu: {
        title: 'Active Sites',
        items: [
          { name: 'Add New Site', path: '/sites/new', action: 'add' },
          ...sites.map(site => ({
            name: site.name,
            path: `/sites/${site.id}/dashboard`,
            action: 'view' as const,
            siteData: site // Add site data for hover preview
          }))
        ],
      },
    },
  {
    name: 'Settings',
    icon: <Settings className="h-6 w-6" />,
    path: '/settings',
    submenu: {
      title: 'Company Settings',
      items: [
        { name: 'User Management', path: '/settings#users' },
        { name: 'Roles & Permissions', path: '/settings#roles' },
        { name: 'Company Profile', path: '/settings#company' },
        { name: 'System Config', path: '/settings#system' },
        { name: 'Compliance', path: '/settings#compliance' },
        { name: 'Integrations', path: '/settings#integrations' },
        { name: 'Notifications', path: '/settings#notifications' },
      ],
    },
  },
  {
    name: 'Data',
    icon: <Database className="h-6 w-6" />,
    path: '/data',
    submenu: {
      title: 'Master Data Management',
      items: [
        { name: 'Training Programs', path: '/data#training-programs' },
        { name: 'PPE Catalog', path: '/data#ppe-catalog' },
        { name: 'Permit Types', path: '/data#permit-types' },
        { name: 'Incident Classifications', path: '/data#incident-types' },
        { name: 'Form Templates', path: '/data#form-templates' },
        { name: 'Trades & Skills', path: '/data#trades-skills' },
      ],
    },
  },
  {
    name: 'Documents',
    icon: <FileText className="h-6 w-6" />,
    path: '/documents',
    submenu: {
      title: 'Document Management',
      items: [
        { name: 'Document Library', path: '/documents#library' },
        { name: 'Compliance Tracker', path: '/documents#compliance' },
        { name: 'Document Categories', path: '/documents#categories' },
        { name: 'Upload Document', path: '/documents#upload', action: 'add' },
      ],
    },
  },
  {
    name: 'Reports',
    icon: <BarChart3 className="h-6 w-6" />,
    path: '/company-reports',
    submenu: {
      title: 'Company Reports',
      items: [
        { name: 'Site Performance', path: '/company-reports#cross-site-performance' },
        { name: 'Incident Statistics', path: '/company-reports#incident-statistics' },
        { name: 'Training Compliance', path: '/company-reports#training-compliance' },
      ],
    },
  },
];
};

/**
 * Returns the site-specific menu items based on the provided siteId
 */
const getSiteMenuItems = (siteId: string | null): SiteMenuItem[] => {
	if (!siteId) return [];

	// Get site data for the current site
	// const sites = getAllSites(); // Commented out as not currently used

  return [
    {
      name: 'Overview',
      icon: <LayoutDashboard className="h-6 w-6" />,
      path: getSitePath(siteId, '/dashboard'),
      submenu: {
        title: 'Site Overview',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/dashboard'), action: 'view' },
          { name: 'Site Details', path: getSitePath(siteId, '/info'), action: 'view' },
          { name: 'Weather', path: getSitePath(siteId, '/weather'), action: 'view' },
        ],
      },
    },
    {
      name: 'Workers',
      icon: <Users className="h-6 w-6" />,
      path: getSitePath(siteId, '/workers'),
      submenu: {
        title: 'Worker Management',
        items: [
          { name: 'Worker Directory', path: getSitePath(siteId, '/workers'), action: 'view' },
          { name: 'Add New Worker', path: getSitePath(siteId, '/workers/new'), action: 'add' },
          { name: 'Import Workers', path: getSitePath(siteId, '/workers/import'), action: 'add' },
          { name: 'Worker Reports', path: getSitePath(siteId, '/workers/reports'), action: 'view' },
        ],
      },
    },
    {
      name: 'Time',
      icon: <Clock className="h-6 w-6" />,
      path: getSitePath(siteId, '/time'),
      submenu: {
        title: 'Time & Attendance',
        items: [
          { name: 'Daily Attendance', path: getSitePath(siteId, '/time#attendance'), action: 'view' },
          { name: 'Overtime Requests', path: getSitePath(siteId, '/time#overtime'), action: 'manage' },
          { name: 'Time Reports', path: getSitePath(siteId, '/time#reports'), action: 'view' },
          { name: 'System', path: getSitePath(siteId, '/time#system'), action: 'manage' },
        ],
      },
    },
    {
      name: 'Training',
      icon: <GraduationCap className="h-6 w-6" />,
      path: getSitePath(siteId, '/training'),
      submenu: {
        title: 'Training & Development',
        items: [
          { name: 'Training Schedule', path: getSitePath(siteId, '/training'), action: 'view' },
          { name: 'Schedule Training', path: getSitePath(siteId, '/training/new'), action: 'add' },
          { name: 'Training Records', path: getSitePath(siteId, '/training/records'), action: 'view' },
          { name: 'Competency Matrix', path: getSitePath(siteId, '/training/competency'), action: 'manage' },
        ],
      },
    },
    {
      name: 'Tasks',
      icon: <ClipboardList className="h-6 w-6" />,
      path: getSitePath(siteId, '/tasks'),
      submenu: {
        title: 'Task Management',
        items: [
          { name: 'Create Task', path: getSitePath(siteId, '/tasks/create'), action: 'add' },
          { name: 'Dashboard', path: getSitePath(siteId, '/tasks#dashboard'), action: 'view' },
          { name: 'Requests', path: getSitePath(siteId, '/tasks#requests'), action: 'manage' },
          { name: 'Active Tasks', path: getSitePath(siteId, '/tasks#active'), action: 'view' },
          { name: 'Task History', path: getSitePath(siteId, '/tasks#history'), action: 'view' },
          { name: 'Templates', path: getSitePath(siteId, '/tasks#templates'), action: 'manage' },
        ],
      },
    },
    {
      name: 'Permits',
      icon: <ClipboardCheck className="h-6 w-6" />,
      path: getSitePath(siteId, '/permits'),
      submenu: {
        title: 'Permits',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/permits#dashboard'), action: 'view' },
          { name: 'Active Permits', path: getSitePath(siteId, '/permits#active-permits'), action: 'view' },
          { name: 'All Permits', path: getSitePath(siteId, '/permits#all-permits'), action: 'view' },
          { name: 'Permit History', path: getSitePath(siteId, '/permits#history'), action: 'view' },
          { name: 'Reports', path: getSitePath(siteId, '/permits#reports'), action: 'view' },
        ],
      },
    },

    {
      name: 'Safety',
      icon: <ShieldCheck className="h-6 w-6" />,
      path: getSitePath(siteId, '/safety'),
      submenu: {
        title: 'Health & Safety',
        items: [
          { name: 'Safety Dashboard', path: getSitePath(siteId, '/safety#dashboard'), action: 'view' },
          { name: 'Incident Management', path: getSitePath(siteId, '/safety#incidents'), action: 'view' },
          { name: 'Toolbox Talks', path: getSitePath(siteId, '/safety#toolbox-talks'), action: 'manage' },
          { name: 'Safety Observations', path: getSitePath(siteId, '/safety#observations'), action: 'view' },
          { name: 'Site RAMS', path: getSitePath(siteId, '/safety#rams'), action: 'manage' },
        ],
      },
    },
    {
      name: 'Inspections',
      icon: <ClipboardList className="h-6 w-6" />,
      path: getSitePath(siteId, '/inspections'),
      submenu: {
        title: 'Inspections',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/inspections#dashboard'), action: 'view' },
          { name: 'Conduct Inspection', path: getSitePath(siteId, '/inspections#conduct'), action: 'add' },
          { name: 'History & Reports', path: getSitePath(siteId, '/inspections#history'), action: 'view' },
          { name: 'My Inspections', path: getSitePath(siteId, '/inspections#my-inspections'), action: 'view' },
        ],
      },
    },
    {
      name: 'Equipment',
      icon: <HardHat className="h-6 w-6" />,
      path: getSitePath(siteId, '/equipment'),
      submenu: {
        title: 'Equipment Management',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/equipment#dashboard'), action: 'view' },
          { name: 'General Equipment', path: getSitePath(siteId, '/equipment#general-equipment'), action: 'view' },
          { name: 'Inspections', path: getSitePath(siteId, '/equipment#inspections'), action: 'view' },
          { name: 'Maintenance', path: getSitePath(siteId, '/equipment#maintenance'), action: 'manage' },
          { name: 'Analytics', path: getSitePath(siteId, '/equipment#analytics'), action: 'view' },
        ],
      },
    },
    {
      name: 'PPE',
      icon: <ShieldCheck className="h-6 w-6" />,
      path: getSitePath(siteId, '/ppe'),
      submenu: {
        title: 'PPE Management',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/ppe#dashboard'), action: 'view' },
          { name: 'PPE Inventory', path: getSitePath(siteId, '/ppe#inventory'), action: 'manage' },
          { name: 'Maintenance', path: getSitePath(siteId, '/ppe#maintenance'), action: 'manage' },
          { name: 'PPE-Worker Relation', path: getSitePath(siteId, '/ppe#worker-relation'), action: 'view' },
        ],
      },
    },
    {
      name: 'Documents',
      icon: <FileText className="h-6 w-6" />,
      path: getSitePath(siteId, '/documents'),
      submenu: {
        title: 'Document Management',
        items: [
          { name: 'Document Library', path: getSitePath(siteId, '/documents'), action: 'view' },
          { name: 'Upload Document', path: getSitePath(siteId, '/documents/upload'), action: 'add' },
          { name: 'Document Categories', path: getSitePath(siteId, '/documents/categories'), action: 'manage' },
        ],
      },
    },
    {
      name: 'Forms',
      icon: <ClipboardList className="h-6 w-6" />,
      path: getSitePath(siteId, '/forms'),
      submenu: {
        title: 'Form Management',
        items: [
          { name: 'Dashboard', path: getSitePath(siteId, '/forms#dashboard'), action: 'view' },
          { name: 'Available Forms', path: getSitePath(siteId, '/forms#available'), action: 'view' },
          { name: 'My Submissions', path: getSitePath(siteId, '/forms#submissions'), action: 'view' },
          { name: 'Create Submission', path: getSitePath(siteId, '/forms#create'), action: 'add' },
        ],
      },
    },
  ];
};

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const SidebarContext = createContext<SidebarContextValue | null>(null);

// ============================================================================
// REDUCER
// ============================================================================

const initialState: SidebarState = {
	expandedMenu: null,
	isHovering: false,
	flyoutVisible: false,
	keyboardNavigation: false,
	focusedItemId: null,
};

function sidebarReducer(
	state: SidebarState,
	action: SidebarAction,
): SidebarState {
	switch (action.type) {
		case "EXPAND_MENU":
			return {
				...state,
				expandedMenu: action.payload,
				flyoutVisible: true,
				isHovering: true,
			};

		case "COLLAPSE_MENU":
			return {
				...state,
				expandedMenu: null,
				flyoutVisible: false,
				isHovering: false,
			};

		case "SET_HOVER":
			return {
				...state,
				isHovering: action.payload,
				flyoutVisible: action.payload && state.expandedMenu !== null,
			};

		case "SET_FLYOUT_VISIBLE":
			return {
				...state,
				flyoutVisible: action.payload,
			};

		case "SET_KEYBOARD_NAV":
			return {
				...state,
				keyboardNavigation: action.payload,
			};

		case "SET_FOCUSED_ITEM":
			return {
				...state,
				focusedItemId: action.payload,
			};

		case "RESET":
			return initialState;

		default:
			return state;
	}
}

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export const SidebarProvider: React.FC<SidebarProviderProps> = ({
	children,
	hoverConfig: customHoverConfig,
}) => {
	const location = useLocation();
	const { isSiteLevel, siteId } = useSiteContext();
	const [state, dispatch] = useReducer(sidebarReducer, initialState);

	// Merge custom hover config with defaults
	const hoverConfig: HoverConfig = useMemo(
		() => ({ ...defaultHoverConfig, ...customHoverConfig }),
		[customHoverConfig],
	);

	// Memoize menu items to prevent unnecessary recalculations
	const menuItems = useMemo(() => {
		const mainItems = getMainMenuItems();
		const siteItems = getSiteMenuItems(siteId || null);
		return isSiteLevel ? siteItems : mainItems;
	}, [isSiteLevel, siteId]);

	// Memoize active menu item for flyout
	const activeMenuItem = useMemo(() => {
		if (!state.expandedMenu) return null;
		return menuItems.find((item) => item.name === state.expandedMenu) || null;
	}, [state.expandedMenu, menuItems]);

	// Memoized function to check if a menu item is active
	const isMenuItemActive = useCallback(
		(item: MenuItem | SiteMenuItem): boolean => {
			if (isPathActive(location.pathname, item.path)) {
				return true;
			}

			// Check if any submenu item is active
			if (item.submenu) {
				return isSubmenuActive(location.pathname, item.submenu.items);
			}

			return false;
		},
		[location.pathname],
	);

	// Context value
	const contextValue: SidebarContextValue = useMemo(
		() => ({
			state,
			dispatch,
			menuItems,
			activeMenuItem,
			isMenuItemActive,
			tokens: sidebarTokens,
			config: hoverConfig,
		}),
		[state, menuItems, activeMenuItem, isMenuItemActive, hoverConfig],
	);

	return (
		<SidebarContext.Provider value={contextValue}>
			{children}
		</SidebarContext.Provider>
	);
};

// ============================================================================
// CUSTOM HOOKS
// ============================================================================

/**
 * Hook to access sidebar context
 */
export const useSidebar = (): SidebarContextValue => {
	const context = useContext(SidebarContext);
	if (!context) {
		throw new Error("useSidebar must be used within a SidebarProvider");
	}
	return context;
};

/**
 * Hook for hover behavior management
 */
export const useSidebarHover = () => {
	const { dispatch, config } = useSidebar();

	// Use refs to track timeouts and prevent race conditions
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	const clearExistingTimeout = useCallback(() => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
	}, []);

	const handleMouseEnter = useCallback(
		(itemName?: string) => {
			clearExistingTimeout();
			if (itemName) {
				dispatch({ type: "EXPAND_MENU", payload: itemName });
			} else {
				dispatch({ type: "SET_HOVER", payload: true });
			}
		},
		[dispatch, clearExistingTimeout],
	);

	const handleMouseLeave = useCallback(() => {
		clearExistingTimeout();
		// Use a timeout to allow for smooth transitions between sidebar and flyout
		timeoutRef.current = setTimeout(() => {
			dispatch({ type: "COLLAPSE_MENU" });
			timeoutRef.current = null;
		}, config.sidebarLeaveDelay);
	}, [dispatch, config.sidebarLeaveDelay, clearExistingTimeout]);

	const handleFlyoutMouseEnter = useCallback(() => {
		clearExistingTimeout();
		// Ensure flyout stays visible when hovering over it
		dispatch({ type: "SET_HOVER", payload: true });
	}, [dispatch, clearExistingTimeout]);

	const handleFlyoutMouseLeave = useCallback(() => {
		clearExistingTimeout();
		timeoutRef.current = setTimeout(() => {
			dispatch({ type: "COLLAPSE_MENU" });
			timeoutRef.current = null;
		}, config.flyoutLeaveDelay);
	}, [dispatch, config.flyoutLeaveDelay, clearExistingTimeout]);

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			clearExistingTimeout();
		};
	}, [clearExistingTimeout]);

	return {
		handleMouseEnter,
		handleMouseLeave,
		handleFlyoutMouseEnter,
		handleFlyoutMouseLeave,
	};
};

/**
 * Hook for keyboard navigation
 */
export const useSidebarKeyboard = () => {
	const { state, dispatch } = useSidebar();

	const handleKeyDown = useCallback(
		(event: React.KeyboardEvent) => {
			const { key } = event;

			switch (key) {
				case "Escape":
					dispatch({ type: "COLLAPSE_MENU" });
					break;

				case "ArrowDown":
				case "ArrowUp":
					event.preventDefault();
					dispatch({ type: "SET_KEYBOARD_NAV", payload: true });
					// Additional keyboard navigation logic would go here
					break;

				case "Enter":
				case " ":
					if (state.focusedItemId) {
						event.preventDefault();
						dispatch({ type: "EXPAND_MENU", payload: state.focusedItemId });
					}
					break;
			}
		},
		[dispatch, state.focusedItemId],
	);

	const setFocusedItem = useCallback(
		(itemId: string | null) => {
			dispatch({ type: "SET_FOCUSED_ITEM", payload: itemId });
		},
		[dispatch],
	);

	return {
		handleKeyDown,
		setFocusedItem,
		isKeyboardNavActive: state.keyboardNavigation,
		focusedItemId: state.focusedItemId,
	};
};

/**
 * Hook for accessibility props
 */
export const useSidebarAccessibility = () => {
	const { state } = useSidebar();

	const getMenuItemProps = useCallback(
		(item: MenuItem | SiteMenuItem, _index: number) => ({
			"aria-haspopup": item.submenu ? ("true" as const) : ("false" as const),
			"aria-expanded":
				state.expandedMenu === item.name
					? ("true" as const)
					: ("false" as const),
			role: "menuitem" as const,
			tabIndex: 0,
		}),
		[state.expandedMenu],
	);

	const getFlyoutProps = useCallback(
		(menuItem: MenuItem | SiteMenuItem) => ({
			role: "menu" as const,
			"aria-label": menuItem.submenu?.title || "Submenu",
			tabIndex: 0,
		}),
		[],
	);

	const getSubmenuItemProps = useCallback(
		(_item: any) => ({
			role: "menuitem" as const,
			tabIndex: 0,
		}),
		[],
	);

	return {
		getMenuItemProps,
		getFlyoutProps,
		getSubmenuItemProps,
	};
};

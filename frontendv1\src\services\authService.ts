import { User, LoginCredentials, RegisterData, AuthSession, SiteEngineer, AppUser } from '../types/auth';

// Demo user data
const DEMO_USER: User = {
  id: 'demo-user-1',
  email: '<EMAIL>',
  firstName: 'Demo',
  lastName: 'User',
  phone: '+254 700 123 456',
  avatar: undefined,
  role: {
    id: 'admin',
    name: 'Administrator',
    permissions: ['read', 'write', 'delete', 'admin']
  },
  tenantId: 'tenant-1',
  status: 'active',
  lastLogin: new Date().toISOString(),
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: new Date().toISOString()
};

// Demo site engineer user
const DEMO_SITE_ENGINEER: SiteEngineer = {
  id: 'engineer-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+254712345678',
  employeeId: 'ENG001',
  siteId: 'site-1',
  siteName: 'Riverside Residential Complex',
  role: 'site-engineer',
  permissions: [
    'view_workers',
    'submit_overtime',
    'create_tasks',
    'request_permits',
    'submit_reports'
  ],
  supervisedWorkers: ['worker-1', 'worker-2', 'worker-3', 'worker-4'],
  createdAt: new Date('2023-01-15')
};

// Mock users database
const MOCK_USERS: AppUser[] = [DEMO_USER, DEMO_SITE_ENGINEER];

class AuthService {
  private static instance: AuthService;
  private readonly SESSION_KEY = 'auth_session';
  private readonly DEMO_PASSWORD = 'password123';

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Simulate network delay
  private async delay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Generate mock JWT token
  private generateToken(): string {
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({ 
      sub: DEMO_USER.id, 
      email: DEMO_USER.email,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    }));
    const signature = btoa('mock-signature');
    return `${header}.${payload}.${signature}`;
  }

  // Login with credentials
  async login(credentials: LoginCredentials): Promise<AuthSession> {
    await this.delay(800); // Simulate API call

    const user = MOCK_USERS.find(u => u.email === credentials.email);

    if (!user) {
      throw new Error('Invalid email or password');
    }

    if (credentials.password !== this.DEMO_PASSWORD) {
      throw new Error('Invalid email or password');
    }

    // Check status for regular users, site engineers are always active
    if ('status' in user && user.status !== 'active') {
      throw new Error('Account is inactive. Please contact support.');
    }

    // Update last login for regular users
    if ('lastLogin' in user) {
      user.lastLogin = new Date().toISOString();
      user.updatedAt = new Date().toISOString();
    }

    const session: AuthSession = {
      user,
      token: this.generateToken(),
      expiresAt: new Date(Date.now() + (credentials.rememberMe ? 30 : 1) * 24 * 60 * 60 * 1000).toISOString(),
      rememberMe: credentials.rememberMe || false
    };

    // Store session
    this.storeSession(session);

    return session;
  }

  // Register new user
  async register(data: RegisterData): Promise<AuthSession> {
    await this.delay(1000); // Simulate API call

    // Validate passwords match
    if (data.password !== data.confirmPassword) {
      throw new Error('Passwords do not match');
    }

    // Check if user already exists
    const existingUser = MOCK_USERS.find(u => u.email === data.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Create new user
    const newUser: User = {
      id: `user-${Date.now()}`,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone,
      avatar: undefined,
      role: {
        id: 'user',
        name: 'User',
        permissions: ['read', 'write']
      },
      tenantId: data.tenantId,
      status: 'active',
      lastLogin: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to mock database
    MOCK_USERS.push(newUser);

    const session: AuthSession = {
      user: newUser,
      token: this.generateToken(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      rememberMe: false
    };

    // Store session
    this.storeSession(session);

    return session;
  }

  // Update user profile
  async updateProfile(userId: string, data: Partial<User>): Promise<User> {
    await this.delay(500); // Simulate API call

    const userIndex = MOCK_USERS.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      throw new Error('User not found');
    }

    // Update user data
    MOCK_USERS[userIndex] = {
      ...MOCK_USERS[userIndex],
      ...data,
      updatedAt: new Date().toISOString()
    };

    // Update stored session if it exists
    const session = this.getStoredSession();
    if (session && session.user.id === userId) {
      session.user = MOCK_USERS[userIndex];
      this.storeSession(session);
    }

    return MOCK_USERS[userIndex];
  }

  // Store session in localStorage
  private storeSession(session: AuthSession): void {
    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
  }

  // Get stored session
  getStoredSession(): AuthSession | null {
    try {
      const stored = localStorage.getItem(this.SESSION_KEY);
      if (!stored) return null;

      const session: AuthSession = JSON.parse(stored);
      
      // Check if session is expired
      if (new Date() > new Date(session.expiresAt)) {
        this.clearSession();
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error parsing stored session:', error);
      this.clearSession();
      return null;
    }
  }

  // Clear session
  clearSession(): void {
    localStorage.removeItem(this.SESSION_KEY);
  }

  // Logout
  logout(): void {
    this.clearSession();
  }

  // Validate session token (mock implementation)
  async validateToken(token: string): Promise<boolean> {
    await this.delay(200);
    // In a real app, this would validate with the backend
    return token.startsWith('eyJ'); // Simple mock validation
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    await this.delay(1500); // Simulate API call

    // Check if user exists
    const user = MOCK_USERS.find(u => u.email === email);
    if (!user) {
      // In a real app, you might not want to reveal if the email exists
      // For demo purposes, we'll throw an error
      throw new Error('No account found with this email address');
    }

    // In a real app, this would send an email with a reset token
    console.log(`Password reset email would be sent to: ${email}`);
  }

  // Reset password with token (mock implementation)
  async resetPassword(token: string, _newPassword: string): Promise<void> {
    await this.delay(1000); // Simulate API call

    // In a real app, this would validate the reset token and update the password
    // For demo purposes, we'll just simulate success
    console.log(`Password would be reset for token: ${token}`);
  }
}

export const authService = AuthService.getInstance();

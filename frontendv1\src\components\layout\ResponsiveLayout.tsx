import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useResponsive from '../../hooks/useResponsive';
import { useAuth } from '../../hooks/useAuthContext';
import { isSiteEngineer } from '../../types/auth';
import Sidebar from './Sidebar';
import MobileLayout from './MobileLayout';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children, title }) => {
  const { isMobile } = useResponsive();
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Determine if user is a site engineer
  const isEngineer = isSiteEngineer(user);

  // Map routes to tab IDs for mobile navigation
  const getActiveTabFromRoute = (pathname: string): string => {
    if (pathname.includes('/site-engineer/dashboard')) return 'dashboard';
    if (pathname.includes('/site-engineer/team')) return 'team';
    if (pathname.includes('/site-engineer/tasks')) return 'tasks';
    if (pathname.includes('/site-engineer/permits')) return 'permits';
    if (pathname.includes('/site-engineer/reports')) return 'reports';
    return 'dashboard';
  };

  // Handle tab changes for mobile navigation
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    
    // Navigate to appropriate route
    const routes: Record<string, string> = {
      dashboard: '/site-engineer/dashboard',
      team: '/site-engineer/team',
      tasks: '/site-engineer/tasks',
      permits: '/site-engineer/permits',
      reports: '/site-engineer/reports'
    };

    if (routes[tabId]) {
      navigate(routes[tabId]);
    }
  };

  // Get page title based on route
  const getPageTitle = (): string => {
    if (title) return title;
    
    const pathname = location.pathname;
    if (pathname.includes('/site-engineer/dashboard')) return 'Dashboard';
    if (pathname.includes('/site-engineer/team')) return 'My Team';
    if (pathname.includes('/site-engineer/tasks')) return 'Tasks';
    if (pathname.includes('/site-engineer/permits')) return 'Permits';
    if (pathname.includes('/site-engineer/reports')) return 'Reports';
    
    return 'Dashboard';
  };

  // Update active tab when route changes
  React.useEffect(() => {
    const currentTab = getActiveTabFromRoute(location.pathname);
    setActiveTab(currentTab);
  }, [location.pathname]);

  // For mobile site engineers, use mobile layout
  if (isMobile && isEngineer) {
    return (
      <MobileLayout
        title={getPageTitle()}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        notificationCount={3}
      >
        {children}
      </MobileLayout>
    );
  }

  // For desktop or non-site engineers, use sidebar layout
  return (
    <div className="bg-[#f3f2ee] min-h-screen font-[Inter]">
      <Sidebar />
      <main className="min-h-screen">
        {children}
      </main>
    </div>
  );
};

export default ResponsiveLayout;

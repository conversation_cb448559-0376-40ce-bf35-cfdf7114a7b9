import React from 'react';
import { Users, CheckSquare, Shield, Clock, Alert<PERSON>riangle, TrendingUp } from 'lucide-react';
import MobileLayout from '../components/layout/MobileLayout';
import MobileCard from '../components/common/MobileCard';
import MobileStatusBadge from '../components/common/MobileStatusBadge';
import useResponsive from '../hooks/useResponsive';

// Local type definitions and mock data
interface Worker {
  id: string;
  name: string;
  isOnSite: boolean;
}

interface Task {
  id: string;
  title: string;
  location: string;
  plannedDate: Date;
  estimatedHours: number;
  assignedWorkers: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed' | 'blocked';
}

interface PermitRequest {
  id: string;
  workActivity: string;
  location: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
}

const mockWorkers: Worker[] = [
  { id: 'worker-1', name: '<PERSON>', isOnSite: true },
  { id: 'worker-2', name: '<PERSON>', isOnSite: true },
  { id: 'worker-3', name: '<PERSON> Ochieng', isOnSite: false },
  { id: 'worker-4', name: 'Grace Muthoni', isOnSite: true }
];

const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: 'Concrete Foundation Pour - Block A',
    location: 'Block A Foundation',
    plannedDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
    estimatedHours: 8,
    assignedWorkers: ['worker-1', 'worker-3', 'worker-4'],
    priority: 'high',
    status: 'approved'
  },
  {
    id: 'task-2',
    title: 'Electrical Panel Installation',
    location: 'Block B Electrical Room',
    plannedDate: new Date(),
    estimatedHours: 6,
    assignedWorkers: ['worker-2'],
    priority: 'medium',
    status: 'in-progress'
  },
  {
    id: 'task-3',
    title: 'Structural Steel Welding',
    location: 'Block A Second Floor',
    plannedDate: new Date(),
    estimatedHours: 4,
    assignedWorkers: ['worker-1'],
    priority: 'critical',
    status: 'in-progress'
  }
];

const mockPermitRequests: PermitRequest[] = [
  {
    id: 'permit-1',
    workActivity: 'Hot Work - Welding',
    location: 'Block A Second Floor',
    priority: 'critical',
    status: 'approved'
  },
  {
    id: 'permit-2',
    workActivity: 'Electrical Installation',
    location: 'Block B Electrical Room',
    priority: 'medium',
    status: 'under-review'
  }
];

interface SiteEngineerDashboardProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const SiteEngineerDashboard: React.FC<SiteEngineerDashboardProps> = ({ activeTab, onTabChange }) => {
  const { isMobile } = useResponsive();

  // Calculate dashboard statistics
  const stats = {
    totalWorkers: mockWorkers.length,
    workersOnSite: mockWorkers.filter(w => w.isOnSite).length,
    activeTasks: mockTasks.filter(t => t.status === 'in-progress').length,
    pendingTasks: mockTasks.filter(t => t.status === 'submitted').length,
    activePermits: mockPermitRequests.filter(p => p.status === 'approved' || p.status === 'active').length,
    pendingPermits: mockPermitRequests.filter(p => p.status === 'submitted' || p.status === 'under-review').length
  };

  const todaysTasks = mockTasks.filter(task => {
    const today = new Date();
    const taskDate = new Date(task.plannedDate);
    return taskDate.toDateString() === today.toDateString();
  });

  const urgentItems = [
    ...mockTasks.filter(t => t.priority === 'critical' && t.status !== 'completed'),
    ...mockPermitRequests.filter(p => p.priority === 'critical' && p.status !== 'approved')
  ];

  // If not mobile, show a message or redirect
  if (!isMobile) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Site Engineer Dashboard</h2>
          <p className="text-gray-600">This view is optimized for mobile devices. Please use a mobile device or resize your browser window.</p>
        </div>
      </div>
    );
  }

  return (
    <MobileLayout
      title="Dashboard"
      activeTab={activeTab}
      onTabChange={onTabChange}
      notificationCount={3}
    >
      <div className="p-4 space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
          <h2 className="text-xl font-semibold mb-2">Good Morning, Engineer!</h2>
          <p className="text-blue-100">
            You have {stats.activeTasks} active tasks and {stats.workersOnSite} workers on site today.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <MobileCard padding="md" clickable onClick={() => onTabChange('team')}>
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.workersOnSite}</p>
                <p className="text-sm text-gray-500">Workers On Site</p>
              </div>
            </div>
          </MobileCard>

          <MobileCard padding="md" clickable onClick={() => onTabChange('tasks')}>
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <CheckSquare className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.activeTasks}</p>
                <p className="text-sm text-gray-500">Active Tasks</p>
              </div>
            </div>
          </MobileCard>

          <MobileCard padding="md" clickable onClick={() => onTabChange('permits')}>
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Shield className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.activePermits}</p>
                <p className="text-sm text-gray-500">Active Permits</p>
              </div>
            </div>
          </MobileCard>

          <MobileCard padding="md" clickable onClick={() => onTabChange('reports')}>
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">85%</p>
                <p className="text-sm text-gray-500">Progress</p>
              </div>
            </div>
          </MobileCard>
        </div>

        {/* Urgent Items */}
        {urgentItems.length > 0 && (
          <MobileCard title="Urgent Items" padding="md">
            <div className="space-y-3">
              {urgentItems.slice(0, 3).map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-red-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {'title' in item ? item.title : item.workActivity}
                      </p>
                      <p className="text-sm text-gray-500">
                        {'location' in item ? item.location : 'Permit Request'}
                      </p>
                    </div>
                  </div>
                  <MobileStatusBadge status="critical" size="sm" />
                </div>
              ))}
            </div>
          </MobileCard>
        )}

        {/* Today's Tasks */}
        <MobileCard title="Today's Tasks" padding="md">
          {todaysTasks.length > 0 ? (
            <div className="space-y-3">
              {todaysTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{task.title}</p>
                    <p className="text-sm text-gray-500">{task.location}</p>
                    <div className="flex items-center mt-2">
                      <Clock className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-500">{task.estimatedHours}h</span>
                      <span className="mx-2 text-gray-300">•</span>
                      <Users className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-500">{task.assignedWorkers.length} workers</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <MobileStatusBadge status={task.status} size="sm" type="task" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No tasks scheduled for today</p>
            </div>
          )}
        </MobileCard>
      </div>
    </MobileLayout>
  );
};

export default SiteEngineerDashboard;

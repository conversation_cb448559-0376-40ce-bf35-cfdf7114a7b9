export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  role: {
    id: string;
    name: string;
    permissions: string[];
  };
  tenantId: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

// Site Engineer specific user interface
export interface SiteEngineer {
  id: string;
  name: string;
  email: string;
  phone: string;
  employeeId: string;
  siteId: string;
  siteName: string;
  role: 'site-engineer';
  permissions: string[];
  supervisedWorkers: string[];
  createdAt: Date;
}

// Union type for all user types
export type AppUser = User | SiteEngineer;

export interface AuthState {
  user: AppUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
  tenantId: string;
}

export interface AuthContextValue {
  user: AppUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<AppUser>) => Promise<void>;
  clearError: () => void;
}

export interface AuthSession {
  user: AppUser;
  token: string;
  expiresAt: string;
  rememberMe: boolean;
}

export enum RolesType {
	ADMIN,
	SITE_SAFETY_OFFICER,
	SITE_AGENT,
	SITE_ENGINEER,
}

export type UserType = {
	role: RolesType;
};

// Utility functions for user type detection
export const isSiteEngineer = (user: AppUser | null): user is SiteEngineer => {
  return user !== null && 'employeeId' in user && user.role === 'site-engineer';
};

export const isRegularUser = (user: AppUser | null): user is User => {
  return user !== null && 'firstName' in user && typeof user.role === 'object';
};

// Authentication Context Type for Site Engineer
export interface SiteEngineerAuthContextType {
  user: SiteEngineer | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}